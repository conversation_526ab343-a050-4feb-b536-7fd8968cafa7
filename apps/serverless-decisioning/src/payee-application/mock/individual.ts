export const IndividualFiling = {
  data: {
    meta: {
      auth: {
        context: {
          account_id: null,
          business_id: null,
          enterprise_id: "ent_2MvQlZSIR823Wh0NXPRjCUMiorb",
          partner_id: null,
          tenant_id: "tnt_2MvQlZSIR823Wh0NXPRjCUMiorb"
        },
        data: {
          business_id: null,
          enterprise_id: null,
          merchant_id: null,
          partner_id: null,
          self_id: null,
          self_subtype: null,
          self_type: "SERVICE",
          tenant_id: null
        }
      }
    },
    object: {
      // Individual data is in the company field (like your JSON 2 example)
      company: {
        application_id: "ctrcapp_2yLmky7SiE6r7PL9fDvnE4HWpzP",
        first_name: "<PERSON>",
        last_name: "<PERSON><PERSON>",
        email: "<EMAIL>",
        birth_date: "1990-01-01",
        phone_number: "**********",
        address: {
          address_line1: "123 Main St",
          address_line2: "Apt 1",
          city: "San Francisco",
          country: "US",
          state: "CA",
          zip_code: "94105"
        },
        parent_id: "part_2kIE8FjSRQg6qZ58OZHkmADujiM",
        parent_type: "PARTNER",
        plaid_manual_verification: false,
        plaid_public_token: null,
        ssn_encrypted: "1fP+yvPuIoqqq9qsEmMXbKGJer+Bb9vtejgDAcVWpV6xRpAKoSkZZw1zRBBCkKTuvJlUogI="
      },
      individual: null,
      entity_data: {
        created_at: "2025-06-11T06:12:55",
        type: "INDIVIDUAL"
      }
    }
  },
  datacontenttype: "application/json",
  id: "evt_2yLmmq8DxhgprjgGwTM7Gund0ld",
  source: "account_engine_ms.contractor_service",
  specversion: "1.0",
  subject: "ctrcapp_2yLmky7SiE6r7PL9fDvnE4HWpzP",
  time: "2025-06-11T06:13:09.991UTC",
  type: "contractor.application_under_review"
};
