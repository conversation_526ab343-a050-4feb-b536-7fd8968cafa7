export const CompanyFiling = {
  data: {
    meta: {
      auth: {
        context: {
          account_id: null,
          business_id: null,
          enterprise_id: "ent_2MvQlZSIR823Wh0NXPRjCUMiorb",
          partner_id: null,
          tenant_id: "tnt_2MvQlZSIR823Wh0NXPRjCUMiorb"
        },
        data: {
          business_id: null,
          enterprise_id: null,
          merchant_id: null,
          partner_id: null,
          self_id: null,
          self_subtype: null,
          self_type: "SERVICE",
          tenant_id: null
        }
      }
    },
    object: {
      company: {
        application_id: "ctrcapp_2yLmky7SiE6r7PL9fDvnE4HWpzP",
        bank_identity_manual_account_dto: null,
        business_address: {
          address_line1: "630 Schimmel Dam",
          address_line2: "Apt 532",
          city: "Livermore",
          country: "US",
          state: "GA",
          zip_code: "83893"
        },
        email: "<PERSON><PERSON><PERSON><EMAIL>",
        encrypted_tax_id: "4zkY9WyKilXKZKeC7Xp63RV+WtYQ8svTGYhMgGaWIHdi/jgtEPYls1mz5ByksGNJYzt4kCY=",
        legal_name: "C49786 <PERSON>utzer - <PERSON>",
        name: "C49786 <PERSON> - Johns",
        owners: [
          {
            birth_date: "1969-11-30",
            email: "<EMAIL>",
            first_name: "Dawson",
            id: "dawson-hettinger-**********",
            last_name: "Hettinger",
            ownership_percent: 8,
            persisted_ssn: {
              mask: "0909"
            },
            phone_number: "**********",
            residence_address: {
              address_line1: "269 Cross Street",
              address_line2: null,
              city: "Johnsview",
              country: "US",
              state: "NV",
              zip_code: "96672"
            },
            signer: true,
            ssn: "0909",
            ssn_encrypted: "1fP+yvPuIoqqq9qsEmMXbKGJer+Bb9vtejgDAcVWpV6xRpAKoSkZZw1zRBBCkKTuvJlUogI=",
            title: "PRESIDENT"
          }
        ],
        ownership_type: "PRIVATE",
        parent_id: "part_2kIE8FjSRQg6qZ58OZHkmADujiM",
        parent_type: "PARTNER",
        plaid_manual_verification: false,
        plaid_public_token: "public-sandbox-0e22bc4f-35ab-407a-833d-ce647852e8a8",
        tax_id: "7904"
      },
      individual: null,
      entity_data: {
        created_at: "2025-06-11T06:12:55",
        type: "COMPANY"
      }
    }
  },
  datacontenttype: "application/json",
  id: "evt_2yLmmq8DxhgprjgGwTM7Gund0ld",
  source: "account_engine_ms.contractor_service",
  specversion: "1.0",
  subject: "ctrcapp_2yLmky7SiE6r7PL9fDvnE4HWpzP",
  time: "2025-06-11T06:13:09.991UTC",
  type: "contractor.application_under_review"
};
