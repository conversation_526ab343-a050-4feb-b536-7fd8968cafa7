import { DBDClient } from '@dbd/service-client-library';
import { describe, expect, it, vi } from 'vitest';

import { CompanyFiling } from './mock/company.js';
import { IndividualInCompanyField } from './mock/individual-in-company-field.js';
import { toPayeeApplication } from './payee-submitted.mapper.js';

// Mock DBDClient
vi.mock('@dbd/service-client-library');

const mockDBDClient = {
  encryption: { decrypt: vi.fn().mockResolvedValue({ decrypted_data: '*********' }) },
  hierarchy: {
    retrieve: vi.fn().mockResolvedValue({
      business: { id: 'bus_1234', name: 'Test Business' },
      partner: { id: 'part_1234', name: 'Test Partner' },
      tenant: { id: 'tenant_1234', name: 'Test Tenant' },
    }),
  },
} as unknown as DBDClient;

describe('Payee Submitted Mapper', () => {
  it('should process valid company data', async () => {
    const result = await toPayeeApplication(CompanyFiling, mockDBDClient);
    expect(result.success).toBe(true);
    expect(result.data?.company).toBeDefined();
  });

  it('should handle null plaid_public_token', async () => {
    const dataWithNullToken = {
      ...CompanyFiling,
      data: {
        ...CompanyFiling.data,
        object: {
          ...CompanyFiling.data.object,
          company: { ...CompanyFiling.data.object.company, plaid_public_token: null },
        },
      },
    };

    const result = await toPayeeApplication(dataWithNullToken, mockDBDClient);
    expect(result.success).toBe(true);
    expect(result.data?.company?.plaid_public_token).toBeUndefined();
  });

  it('should handle invalid data', async () => {
    const result = await toPayeeApplication({ invalid: 'data' }, mockDBDClient);
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
  });

  it('should handle missing company and individual data', async () => {
    const dataWithoutEntity = {
      ...CompanyFiling,
      data: {
        ...CompanyFiling.data,
        object: { ...CompanyFiling.data.object, company: null, individual: null },
      },
    };

    const result = await toPayeeApplication(dataWithoutEntity, mockDBDClient);
    expect(result.success).toBe(false);
    expect(result.error?.message).toContain('Neither company nor individual data found');
  });

  it('should handle individual data in company field structure', async () => {
    const result = await toPayeeApplication(IndividualInCompanyField, mockDBDClient);
    expect(result.success).toBe(true);
    expect(result.data?.individual).toBeDefined();
    expect(result.data?.company).toBeUndefined();
    expect(result.data?.individual?.first_name).toBe('John');
    expect(result.data?.individual?.last_name).toBe('Doe');
    expect(result.data?.individual?.plaid_public_token).toBeUndefined();
  });

  it('should handle real company data structure with missing fields', async () => {
    // Test with company data that has undefined legal_name and name initially
    const companyWithMissingFields = {
      ...CompanyFiling,
      data: {
        ...CompanyFiling.data,
        object: {
          ...CompanyFiling.data.object,
          company: {
            ...CompanyFiling.data.object.company,
            legal_name: undefined,
            name: undefined,
          },
        },
      },
    };

    const result = await toPayeeApplication(companyWithMissingFields, mockDBDClient);
    expect(result.success).toBe(true);
    expect(result.data?.company).toBeDefined();
    expect(result.data?.individual).toBeUndefined();
    expect(result.data?.company?.legal_name).toBe('Unknown Company');
    expect(result.data?.company?.name).toBe('Unknown Company');
  });
});
