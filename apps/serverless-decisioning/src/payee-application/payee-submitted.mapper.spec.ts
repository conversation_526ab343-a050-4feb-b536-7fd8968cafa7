import { DBDClient } from '@dbd/service-client-library';
import { describe, expect, it, vi } from 'vitest';

import { CompanyFiling } from './mock/company.js';
import { toPayeeApplication } from './payee-submitted.mapper.js';

// Mock DBDClient
vi.mock('@dbd/service-client-library');

const mockDBDClient = {
  encryption: { decrypt: vi.fn().mockResolvedValue({ decrypted_data: '*********' }) },
  hierarchy: {
    retrieve: vi.fn().mockResolvedValue({
      business: { id: 'bus_1234', name: 'Test Business' },
      partner: { id: 'part_1234', name: 'Test Partner' },
      tenant: { id: 'tenant_1234', name: 'Test Tenant' },
    }),
  },
} as unknown as DBDClient;

describe('Payee Submitted Mapper', () => {
  it('should process valid company data', async () => {
    const result = await toPayeeApplication(CompanyFiling, mockDBDClient);
    expect(result.success).toBe(true);
    expect(result.data?.company).toBeDefined();
  });

  it('should handle null plaid_public_token', async () => {
    const dataWithNullToken = {
      ...CompanyFiling,
      data: {
        ...CompanyFiling.data,
        object: {
          ...CompanyFiling.data.object,
          company: { ...CompanyFiling.data.object.company, plaid_public_token: null },
        },
      },
    };

    const result = await toPayeeApplication(dataWithNullToken, mockDBDClient);
    expect(result.success).toBe(true);
    expect(result.data?.company?.plaid_public_token).toBeUndefined();
  });

  it('should handle invalid data', async () => {
    const result = await toPayeeApplication({ invalid: 'data' }, mockDBDClient);
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
  });

  it('should handle missing company and individual data', async () => {
    const dataWithoutEntity = {
      ...CompanyFiling,
      data: {
        ...CompanyFiling.data,
        object: { ...CompanyFiling.data.object, company: null, individual: null },
      },
    };

    const result = await toPayeeApplication(dataWithoutEntity, mockDBDClient);
    expect(result.success).toBe(false);
    expect(result.error?.message).toContain('Neither company nor individual data found');
  });

  it('should handle individual data in company field structure', async () => {
    const individualInCompanyField = {
      ...CompanyFiling,
      data: {
        ...CompanyFiling.data,
        object: {
          ...CompanyFiling.data.object,
          company: {
            application_id: 'test_app_123',
            first_name: 'John',
            last_name: 'Doe',
            email: '<EMAIL>',
            birth_date: '1990-01-01',
            plaid_public_token: null,
            plaid_manual_verification: false,
            parent_type: 'PARTNER',
            parent_id: 'partner_123',
            address: {
              country: 'US',
              state: 'CA',
              city: 'San Francisco',
              zip_code: '94105',
              address_line1: '123 Main St',
              address_line2: 'Apt 1',
            },
          },
          individual: null,
          entity_data: {
            created_at: '2025-06-11T06:12:55',
          },
        },
      },
    };

    const result = await toPayeeApplication(individualInCompanyField, mockDBDClient);
    expect(result.success).toBe(true);
    expect(result.data?.individual).toBeDefined();
    expect(result.data?.company).toBeUndefined();
    expect(result.data?.individual?.first_name).toBe('John');
    expect(result.data?.individual?.last_name).toBe('Doe');
    expect(result.data?.individual?.plaid_public_token).toBeUndefined();
  });
});
