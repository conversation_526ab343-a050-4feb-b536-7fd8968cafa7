import { HierarchyTypeEnum } from '@dbd/core-types/enums/common';
import { PayeeApplicationSubmittedEvent, PayeeCompany, PayeeIndividual, PayeeOnboardingInput } from '@dbd/risk-types';
import { type DBDClient } from '@dbd/service-client-library';
import { Simplify } from 'type-fest';
import { z } from 'zod';

const WrappedPayeeApplicationSubmittedEvent = z.object({
  subject: z.string(),
  data: z.object({
    meta: z.object({
      auth: z.object({
        context: z.object({
          tenant_id: z.string().nullish(),
          account_id: z.string().nullish(),
          partner_id: z.string().nullish(),
          business_id: z.string().nullish(),
          enterprise_id: z.string().nullish(),
        }),
      }),
    }),
    object: z.object({
      company: z.record(z.unknown()).nullable(), // Allow any object structure for raw data
      individual: z.record(z.unknown()).nullable(), // Allow any object structure for raw data
      entity_data: z.object({
        created_at: z.string().nullish(),
        type: z.enum(['COMPANY', 'INDIVIDUAL']).optional(),
      }),
    }),
  }),
});

type WrappedPayeeApplicationSubmittedEvent = Simplify<z.infer<typeof WrappedPayeeApplicationSubmittedEvent>>;

/**
 *
 * @param eventData - Possibly Payee Application data
 * @returns
 */
export async function toPayeeApplication(eventData: unknown, dbdClient: DBDClient) {
  const { data, success, error } = WrappedPayeeApplicationSubmittedEvent.safeParse(eventData);
  if (!success) {
    return { data: null, error, success: false };
  }

  return mapPayload(data, dbdClient);
}

async function mapPayload(eventData: WrappedPayeeApplicationSubmittedEvent, dbdClient: DBDClient) {
  if (isIndividualBoarding(eventData.data.object)) {
    const individual = eventData.data.object.individual;
    if (
      individual &&
      typeof individual === 'object' &&
      'ssn_encrypted' in individual &&
      typeof individual.ssn_encrypted === 'string'
    ) {
      (individual as Record<string, unknown>).ssn = (
        await dbdClient.encryption.decrypt(individual.ssn_encrypted)
      ).decrypted_data;
    }
  }

  if (isCompanyBoarding(eventData.data.object)) {
    const company = eventData.data.object.company;
    if (company && typeof company === 'object') {
      if ('encrypted_tax_id' in company && typeof company.encrypted_tax_id === 'string') {
        (company as Record<string, unknown>).ein = (
          await dbdClient.encryption.decrypt(company.encrypted_tax_id)
        ).decrypted_data;
      }

      if ('owners' in company && Array.isArray(company.owners)) {
        (company as Record<string, unknown>).owners = await Promise.all(
          company.owners.map(async (owner: unknown) => {
            if (typeof owner === 'object' && owner !== null) {
              const ownerObj = owner as Record<string, unknown>;
              return {
                ...ownerObj,
                ssn:
                  'ssn_encrypted' in ownerObj && typeof ownerObj.ssn_encrypted === 'string'
                    ? (await dbdClient.encryption.decrypt(ownerObj.ssn_encrypted)).decrypted_data
                    : undefined,
                ownership_percent: ownerObj.ownership_percent ?? undefined, // Convert null to undefined
              };
            }
            return owner;
          }),
        );
      }
    }
  }

  // Determine entity type and get raw data
  const isCompany = determineEntityType(eventData.data.object);
  const rawData = eventData.data.object.company ?? eventData.data.object.individual;

  if (!rawData) {
    return { data: null, error: new Error('Neither company nor individual data found'), success: false };
  }

  let transformedData: PayeeCompany | PayeeIndividual;

  if (isCompany) {
    // Handle company data structure
    if (typeof rawData === 'object' && rawData !== null) {
      const companyData = rawData as Record<string, unknown>;
      const { plaid_public_token: _plaidPublicToken, ...companyDataWithoutPlaid } = companyData;

      const name =
        (typeof companyData.name === 'string' ? companyData.name : '') ||
        (typeof companyData.legal_name === 'string' ? companyData.legal_name : '') ||
        'Unknown Company';
      const legalName =
        (typeof companyData.legal_name === 'string' ? companyData.legal_name : '') ||
        (typeof companyData.name === 'string' ? companyData.name : '') ||
        'Unknown Company';

      transformedData = {
        ...companyDataWithoutPlaid,
        name,
        legal_name: legalName,
        plaid_manual_verification:
          typeof companyData.plaid_manual_verification === 'boolean' ? companyData.plaid_manual_verification : false,
        business_address: companyData.business_address || companyData.address,
      } as PayeeCompany;
    } else {
      throw new Error('Invalid company data structure');
    }
  } else {
    // Handle individual data structure (when data is in company field but represents individual)
    if (typeof rawData === 'object' && rawData !== null) {
      const individualData = rawData as Record<string, unknown>;
      const { plaid_public_token: _plaidPublicToken, ...individualDataWithoutPlaid } = individualData;

      // Ensure address field exists for individual
      const address = individualData.address ||
        individualData.business_address || {
          country: 'US',
          state: 'CA',
          city: 'Unknown',
          zip_code: '00000',
          address_line1: 'Unknown',
          address_line2: null,
        };

      transformedData = {
        ...individualDataWithoutPlaid,
        plaid_manual_verification:
          typeof individualData.plaid_manual_verification === 'boolean'
            ? individualData.plaid_manual_verification
            : false,
        address,
      } as PayeeIndividual;
    } else {
      throw new Error('Invalid individual data structure');
    }
  }

  const { data, error, success } = PayeeApplicationSubmittedEvent.safeParse(transformedData);

  if (!success) {
    return { data: null, error, success: false };
  }

  const hierarchy = {
    tenant_id: '',
    tenant_name: '',
    partner_id: '',
    partner_name: '',
    business_name: '',
    business_id: '',
  };

  if (data.parent_type === 'PARTNER' && eventData.data.meta.auth.context.partner_id) {
    const res = await dbdClient.hierarchy.retrieve(
      eventData.data.meta.auth.context.partner_id,
      HierarchyTypeEnum.PARTNER,
    );
    hierarchy.partner_id = res.partner.id;
    hierarchy.partner_name = res.partner.name;

    hierarchy.tenant_id = res.tenant.id;
    hierarchy.tenant_name = res.tenant.name;
  } else if (data.parent_type === 'BUSINESS' && eventData.data.meta.auth.context.business_id) {
    const res = await dbdClient.hierarchy.retrieve(
      eventData.data.meta.auth.context.business_id,
      HierarchyTypeEnum.BUSINESS,
    );

    hierarchy.business_id = res.business.id;
    hierarchy.business_name = res.business.name;

    hierarchy.partner_id = res.partner.id;
    hierarchy.partner_name = res.partner.name;

    hierarchy.tenant_id = res.tenant.id;
    hierarchy.tenant_name = res.tenant.name;
  }

  const isCompanyType = isCompanyBoarding(eventData.data.object);

  const input: Partial<PayeeOnboardingInput> = {
    application_id: data.application_id as string,
    ...hierarchy,
    payee_level: data.parent_type as 'PARTNER' | 'BUSINESS',
    payee_id: data.parent_id as string,
    submitted_at: eventData.data.object.entity_data.created_at ?? new Date().toISOString(),
    comments: '',
    ip_address: '',
    company: isCompanyType ? (data as PayeeCompany) : undefined,
    individual: !isCompanyType ? (data as PayeeIndividual) : undefined,
  };
  return PayeeOnboardingInput.safeParse(input);
}

function determineEntityType(objectData: Record<string, unknown>): boolean {
  // First check if entity_data.type is explicitly set
  const entityData = objectData.entity_data;
  if (entityData && typeof entityData === 'object' && 'type' in entityData) {
    return entityData.type === 'COMPANY';
  }

  // Fallback: Check data structure to determine type
  const companyData = objectData.company;
  if (!companyData || typeof companyData !== 'object') {
    return false; // No company data, assume individual
  }

  // If company data has business-specific fields, it's a company
  if (
    ('legal_name' in companyData && companyData.legal_name) ||
    ('name' in companyData && companyData.name) ||
    ('owners' in companyData && companyData.owners) ||
    ('business_address' in companyData && companyData.business_address)
  ) {
    return true;
  }

  // If company data has individual-specific fields, it's actually individual data
  if (
    ('first_name' in companyData && companyData.first_name) ||
    ('last_name' in companyData && companyData.last_name) ||
    ('birth_date' in companyData && companyData.birth_date)
  ) {
    return false;
  }

  // Default to company if company object exists
  return objectData.company !== null;
}

function isCompanyBoarding(value: Record<string, any>): value is PayeeCompany {
  return determineEntityType(value);
}

function isIndividualBoarding(value: Record<string, any>): value is PayeeIndividual {
  return !determineEntityType(value);
}
