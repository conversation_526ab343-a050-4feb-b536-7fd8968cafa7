import { HierarchyTypeEnum } from '@dbd/core-types/enums/common';
import { PayeeApplicationSubmittedEvent, PayeeCompany, PayeeIndividual, PayeeOnboardingInput } from '@dbd/risk-types';
import { type DBDClient } from '@dbd/service-client-library';
import { Simplify } from 'type-fest';
import { z } from 'zod';

const WrappedPayeeApplicationSubmittedEvent = z.object({
  subject: z.string(),
  data: z.object({
    meta: z.object({
      auth: z.object({
        context: z.object({
          tenant_id: z.string().nullish(),
          account_id: z.string().nullish(),
          partner_id: z.string().nullish(),
          business_id: z.string().nullish(),
          enterprise_id: z.string().nullish(),
        }),
      }),
    }),
    object: z.object({
      company: z.record(z.unknown()).nullable(), // Allow any object structure for raw data
      individual: z.record(z.unknown()).nullable(), // Allow any object structure for raw data
      entity_data: z.object({
        created_at: z.string().nullish(),
        type: z.enum(['COMPANY', 'INDIVIDUAL']).optional(),
      }),
    }),
  }),
});

type WrappedPayeeApplicationSubmittedEvent = Simplify<z.infer<typeof WrappedPayeeApplicationSubmittedEvent>>;

/**
 *
 * @param eventData - Possibly Payee Application data
 * @returns
 */
export async function toPayeeApplication(eventData: unknown, dbdClient: DBDClient) {
  const { data, success, error } = WrappedPayeeApplicationSubmittedEvent.safeParse(eventData);
  if (!success) {
    return { data: null, error, success: false };
  }

  return mapPayload(data, dbdClient);
}

async function mapPayload(eventData: WrappedPayeeApplicationSubmittedEvent, dbdClient: DBDClient) {
  if (isIndividualBoarding(eventData.data.object)) {
    const individual = eventData.data.object.individual as any;
    if (individual?.ssn_encrypted) {
      individual.ssn = (
        await dbdClient.encryption.decrypt(individual.ssn_encrypted)
      ).decrypted_data;
    }
  }

  if (isCompanyBoarding(eventData.data.object)) {
    const company = eventData.data.object.company as any;
    if (company?.encrypted_tax_id) {
      company.ein = (await dbdClient.encryption.decrypt(company.encrypted_tax_id)).decrypted_data;
    }

    if (company?.owners && Array.isArray(company.owners)) {
      company.owners = await Promise.all(
        company.owners.map(async (owner: any) => ({
          ...owner,
          ssn: owner.ssn_encrypted
            ? (await dbdClient.encryption.decrypt(owner.ssn_encrypted)).decrypted_data
            : undefined,
          ownership_percent: owner.ownership_percent ?? undefined, // Convert null to undefined
        })),
      );
    }
  }

  // Determine entity type and get raw data
  const isCompany = determineEntityType(eventData.data.object);
  const rawData = eventData.data.object.company ?? eventData.data.object.individual;

  if (!rawData) {
    return { data: null, error: new Error('Neither company nor individual data found'), success: false };
  }

  let transformedData: PayeeCompany | PayeeIndividual;

  if (isCompany) {
    // Handle company data structure
    const companyData = rawData as any;
    const { plaid_public_token, ...companyDataWithoutPlaid } = companyData;
    transformedData = {
      ...companyDataWithoutPlaid,
      // Ensure required fields with fallbacks
      name: companyData.name || companyData.legal_name || 'Unknown Company',
      legal_name: companyData.legal_name || companyData.name || 'Unknown Company',
      plaid_manual_verification: companyData.plaid_manual_verification ?? false,
      // Handle address field mapping
      business_address: companyData.business_address || companyData.address,
    } as PayeeCompany;
  } else {
    // Handle individual data structure (when data is in company field but represents individual)
    const individualData = rawData as any;
    const { plaid_public_token, ...individualDataWithoutPlaid } = individualData;

    // Ensure address field exists for individual
    const address = individualData.address || individualData.business_address || {
      country: 'US',
      state: 'CA',
      city: 'Unknown',
      zip_code: '00000',
      address_line1: 'Unknown',
      address_line2: null,
    };

    transformedData = {
      ...individualDataWithoutPlaid,
      plaid_manual_verification: individualData.plaid_manual_verification ?? false,
      address,
    } as PayeeIndividual;
  }

  const { data, error, success } = PayeeApplicationSubmittedEvent.safeParse(transformedData);

  if (!success) {
    return { data: null, error, success: false };
  }

  const hierarchy = {
    tenant_id: '',
    tenant_name: '',
    partner_id: '',
    partner_name: '',
    business_name: '',
    business_id: '',
  };

  if (data.parent_type === 'PARTNER' && eventData.data.meta.auth.context.partner_id) {
    const res = await dbdClient.hierarchy.retrieve(
      eventData.data.meta.auth.context.partner_id,
      HierarchyTypeEnum.PARTNER,
    );
    hierarchy.partner_id = res.partner.id;
    hierarchy.partner_name = res.partner.name;

    hierarchy.tenant_id = res.tenant.id;
    hierarchy.tenant_name = res.tenant.name;
  } else if (data.parent_type === 'BUSINESS' && eventData.data.meta.auth.context.business_id) {
    const res = await dbdClient.hierarchy.retrieve(
      eventData.data.meta.auth.context.business_id,
      HierarchyTypeEnum.BUSINESS,
    );

    hierarchy.business_id = res.business.id;
    hierarchy.business_name = res.business.name;

    hierarchy.partner_id = res.partner.id;
    hierarchy.partner_name = res.partner.name;

    hierarchy.tenant_id = res.tenant.id;
    hierarchy.tenant_name = res.tenant.name;
  }

  const isCompanyType = isCompanyBoarding(eventData.data.object);

  const input: Partial<PayeeOnboardingInput> = {
    application_id: data.application_id as string,
    ...hierarchy,
    payee_level: data.parent_type as 'PARTNER' | 'BUSINESS',
    payee_id: data.parent_id as string,
    submitted_at: eventData.data.object.entity_data.created_at ?? new Date().toISOString(),
    comments: '',
    ip_address: '',
    company: isCompanyType ? (data as PayeeCompany) : undefined,
    individual: !isCompanyType ? (data as PayeeIndividual) : undefined,
  };
  return PayeeOnboardingInput.safeParse(input);
}

function determineEntityType(objectData: Record<string, any>): boolean {
  // First check if entity_data.type is explicitly set
  if (objectData.entity_data?.type) {
    return objectData.entity_data.type === 'COMPANY';
  }

  // Fallback: Check data structure to determine type
  const companyData = objectData.company;
  if (!companyData) {
    return false; // No company data, assume individual
  }

  // If company data has business-specific fields, it's a company
  if (companyData.legal_name || companyData.name || companyData.owners || companyData.business_address) {
    return true;
  }

  // If company data has individual-specific fields, it's actually individual data
  if (companyData.first_name || companyData.last_name || companyData.birth_date) {
    return false;
  }

  // Default to company if company object exists
  return objectData.company !== null;
}

function isCompanyBoarding(value: Record<string, any>): value is PayeeCompany {
  return determineEntityType(value);
}

function isIndividualBoarding(value: Record<string, any>): value is PayeeIndividual {
  return !determineEntityType(value);
}
