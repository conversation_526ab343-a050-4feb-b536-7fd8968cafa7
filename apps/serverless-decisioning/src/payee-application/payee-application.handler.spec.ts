import { ServerAuthClass } from '@dbd/service-auth';
import { DBDClient } from '@dbd/service-client-library';
import { SNSEvent } from 'aws-lambda';
import { describe, vi } from 'vitest';

import { CompanyFiling } from './mock/company.js';
import { HANDLER_CONTEXT_MOCK_DATA as ctx } from './mock/context.js';
import { IndividualFiling } from './mock/individual.js';
import { main } from './payee-application.handler';

vi.mock('@dbd/service-client-library');

const { useHierarchy } = vi.hoisted(() => ({
  useHierarchy: vi.fn().mockReturnValue(
    Promise.resolve({
      business: {},
      partner: {},
      tenant: {},
    }),
  ),
}));

const { useDecrypt } = vi.hoisted(() => ({
  useDecrypt: vi.fn().mockReturnValue(
    Promise.resolve({
      decrypted_data: '*********',
    }),
  ),
}));

const { useSubmit } = vi.hoisted(() => ({
  useSubmit: vi.fn().mockReturnValue(Promise.resolve({})),
}));

const { usePayeeSubmit } = vi.hoisted(() => ({
  usePayeeSubmit: vi.fn().mockReturnValue(Promise.resolve({})),
}));

const { useServiceJWT } = vi.hoisted(() => ({
  useServiceJWT: vi.fn().mockReturnValue(
    Promise.resolve({
      roles: [],
      data: {
        selfType: 'SERVICE',
        selfSubtype: 'SERVICE',
      },
      context: {},
    }),
  ),
}));

vi.mock('@dbd/serverless-logger', () => {
  return {
    LogWithRequestTracing: vi.fn(),
    Logger: {
      error: vi.fn(),
      info: vi.fn(),
    },
  };
});

describe('Boarding | Payee (Contractor) Application Handler', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('creates a request to send an individual payee application to Taktile', async ({ expect }) => {
    ServerAuthClass.prototype.getServiceJWT = useServiceJWT;
    DBDClient.prototype.boardings = {
      submitToTaktile: useSubmit,
      submitPayeeToTaktile: usePayeeSubmit,
    };
    DBDClient.prototype.encryption = {
      decrypt: useDecrypt,
    };
    DBDClient.prototype.hierarchy = {
      retrieve: useHierarchy.mockReturnValue(
        Promise.resolve({
          business: {
            id: 'bus_1234',
            name: 'my business',
          },
          partner: {
            id: 'prt_1234',
            name: 'my partner',
          },
          tenant: {
            id: 'tnt_1234',
            name: 'my tenant',
          },
        }),
      ),
    };

    const event: SNSEvent = {
      Records: [
        {
          EventSource: 'aws:sns',
          EventVersion: '1.0',
          EventSubscriptionArn: '',
          Sns: {
            Type: 'Notification',
            MessageId: '34263322-01c8-5496-8963-6fe18fa2a4f3',
            TopicArn: 'arn:aws:sns:us-east-1:*********:-development-us-east-1-boarding_payee_application.submitted',
            Subject: '',
            Message: JSON.stringify(IndividualFiling),
            Timestamp: '2023-04-26T20:40:10.056Z',
            SignatureVersion: '1',
            Signature: '',
            SigningCertUrl: '',
            UnsubscribeUrl: '',
            MessageAttributes: {
              id: {
                Type: 'String',
                Value: '8738b2d3-0414-d783-adb3-8b82bb0e7b1d',
              },
            },
          },
        },
      ],
    };

    await expect(main(event, ctx)).resolves.toBeDefined();
  });

  it('creates a request to send an company payee application to Taktile', async ({ expect }) => {
    ServerAuthClass.prototype.getServiceJWT = useServiceJWT;
    DBDClient.prototype.boardings = {
      submitToTaktile: useSubmit,
      submitPayeeToTaktile: usePayeeSubmit,
    };
    DBDClient.prototype.encryption = {
      decrypt: useDecrypt,
    };
    DBDClient.prototype.hierarchy = {
      retrieve: useHierarchy.mockReturnValue(
        Promise.resolve({
          business: {
            id: 'bus_1234',
            name: 'my business',
          },
          partner: {
            id: 'prt_1234',
            name: 'my partner',
          },
          tenant: {
            id: 'tnt_1234',
            name: 'my tenant',
          },
        }),
      ),
    };
    const event: SNSEvent = {
      Records: [
        {
          EventSource: 'aws:sns',
          EventVersion: '1.0',
          EventSubscriptionArn: '',
          Sns: {
            Type: 'Notification',
            MessageId: '34263322-01c8-5496-8963-6fe18fa2a4f3',
            TopicArn: 'arn:aws:sns:us-east-1:*********:-development-us-east-1-boarding_payee_application.submitted',
            Subject: '',
            Message: JSON.stringify(CompanyFiling),
            Timestamp: '2023-04-26T20:40:10.056Z',
            SignatureVersion: '1',
            Signature: '',
            SigningCertUrl: '',
            UnsubscribeUrl: '',
            MessageAttributes: {
              id: {
                Type: 'String',
                Value: '8738b2d3-0414-d783-adb3-8b82bb0e7b1d',
              },
            },
          },
        },
      ],
    };

    await expect(main(event, ctx)).resolves.toBeDefined();
  });

  it('returns an error with an invalid body before sending to the Boarding Service', async ({ expect }) => {
    const event: SNSEvent = {
      Records: [
        {
          EventSource: 'aws:sns',
          EventVersion: '1.0',
          EventSubscriptionArn: '',
          Sns: {
            Type: 'Notification',
            MessageId: '34263322-01c8-5496-8963-6fe18fa2a4f3',
            TopicArn: 'arn:aws:sns:us-east-1:*********:-development-us-east-1-merchant_application.submitted',
            Subject: '',
            Message: JSON.stringify({}),
            Timestamp: '2023-04-26T20:40:10.056Z',
            SignatureVersion: '1',
            Signature: '',
            SigningCertUrl: '',
            UnsubscribeUrl: '',
            MessageAttributes: {
              id: {
                Type: 'String',
                Value: '8738b2d3-0414-d783-adb3-8b82bb0e7b1d',
              },
            },
          },
        },
      ],
    };

    await expect(main(event, ctx)).rejects.toThrowError();
  });

  it('handles individual payee with missing name fields', async ({ expect }) => {
    ServerAuthClass.prototype.getServiceJWT = useServiceJWT;
    DBDClient.prototype.boardings = {
      submitToTaktile: useSubmit,
      submitPayeeToTaktile: usePayeeSubmit,
    };
    DBDClient.prototype.encryption = {
      decrypt: useDecrypt,
    };
    DBDClient.prototype.hierarchy = {
      retrieve: useHierarchy.mockReturnValue(
        Promise.resolve({
          business: {
            id: 'bus_1234',
            name: 'my business',
          },
          partner: {
            id: 'prt_1234',
            name: 'my partner',
          },
          tenant: {
            id: 'tnt_1234',
            name: 'my tenant',
          },
        }),
      ),
    };

    const individualWithMissingNames = {
      ...IndividualFiling,
      data: {
        ...IndividualFiling.data,
        object: {
          ...IndividualFiling.data.object,
          individual: {
            ...IndividualFiling.data.object.individual,
            first_name: undefined,
            last_name: undefined,
            plaid_public_token: null,
          },
        },
      },
    };

    const event: SNSEvent = {
      Records: [
        {
          EventSource: 'aws:sns',
          EventVersion: '1.0',
          EventSubscriptionArn: '',
          Sns: {
            Type: 'Notification',
            MessageId: '34263322-01c8-5496-8963-6fe18fa2a4f3',
            TopicArn: 'arn:aws:sns:us-east-1:*********:-development-us-east-1-boarding_payee_application.submitted',
            Subject: '',
            Message: JSON.stringify(individualWithMissingNames),
            Timestamp: '2023-04-26T20:40:10.056Z',
            SignatureVersion: '1',
            Signature: '',
            SigningCertUrl: '',
            UnsubscribeUrl: '',
            MessageAttributes: {
              id: {
                Type: 'String',
                Value: '8738b2d3-0414-d783-adb3-8b82bb0e7b1d',
              },
            },
          },
        },
      ],
    };

    await expect(main(event, ctx)).resolves.toBeDefined();
  });

  it('handles company payee with missing name fields', async ({ expect }) => {
    ServerAuthClass.prototype.getServiceJWT = useServiceJWT;
    DBDClient.prototype.boardings = {
      submitToTaktile: useSubmit,
      submitPayeeToTaktile: usePayeeSubmit,
    };
    DBDClient.prototype.encryption = {
      decrypt: useDecrypt,
    };
    DBDClient.prototype.hierarchy = {
      retrieve: useHierarchy.mockReturnValue(
        Promise.resolve({
          business: {
            id: 'bus_1234',
            name: 'my business',
          },
          partner: {
            id: 'prt_1234',
            name: 'my partner',
          },
          tenant: {
            id: 'tnt_1234',
            name: 'my tenant',
          },
        }),
      ),
    };

    const companyWithMissingNames = {
      ...CompanyFiling,
      data: {
        ...CompanyFiling.data,
        object: {
          ...CompanyFiling.data.object,
          company: {
            ...CompanyFiling.data.object.company,
            name: undefined,
            legal_name: undefined,
            plaid_public_token: null,
          },
        },
      },
    };

    const event: SNSEvent = {
      Records: [
        {
          EventSource: 'aws:sns',
          EventVersion: '1.0',
          EventSubscriptionArn: '',
          Sns: {
            Type: 'Notification',
            MessageId: '34263322-01c8-5496-8963-6fe18fa2a4f3',
            TopicArn: 'arn:aws:sns:us-east-1:*********:-development-us-east-1-boarding_payee_application.submitted',
            Subject: '',
            Message: JSON.stringify(companyWithMissingNames),
            Timestamp: '2023-04-26T20:40:10.056Z',
            SignatureVersion: '1',
            Signature: '',
            SigningCertUrl: '',
            UnsubscribeUrl: '',
            MessageAttributes: {
              id: {
                Type: 'String',
                Value: '8738b2d3-0414-d783-adb3-8b82bb0e7b1d',
              },
            },
          },
        },
      ],
    };

    await expect(main(event, ctx)).resolves.toBeDefined();
  });
});
